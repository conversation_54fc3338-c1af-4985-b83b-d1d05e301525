var QbiQuickReply = {
  /**
   * @desc Store QuickReply Swiper. Default value is null.
   * @type {any}
   * @default null
   */
  quickReplyPool: null,

  /**
   * @desc Store transform observer. Default value is null.
   * @type {MutationObserver}
   * @default null
   */
  transformObserver: null,

  // QuickReply HTML 模板
  templates: {
    verticalBackground: `
      <div class='QuickreplySlide' style='color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    verticalBorder: `
      <div class='QuickreplySlide' style='color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    horizontalBackground: `
      <div class='swiper-slide QuickreplySlide' style='width: fit-content;color:{color} !important; cursor:pointer; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
    horizontalBorder: `
      <div class='swiper-slide QuickreplySlide' style='width: fit-content;color:{color} !important; background-color: unset !important; border-color:{borderColor} !important;'>
        <img class='QuickReply_img' src='{imgUrl}' style='{imgDisplay}' alt='Quickly Reply'/>
        {entity}
      </div>
    `,
  },

  // Create QuickReply button
  create: function (content) {
    $("#ToolZone").show();
    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        $("#QuickReply").addClass("QuickReply_Vertical");
        break;
      case "Horizontal":
        $("#QuickReply").removeClass("QuickReply_Vertical");
        break;
      default:
        break;
    }

    let quick_reply_item = content.hasOwnProperty("QuickReply")
      ? content.QuickReply.quick_reply_items
      : content.quick_reply_items;

    for (let i = 0; i < quick_reply_item.length; i++) {
      let colorIndex;
      let entity = "";
      let item = quick_reply_item[i];
      let imgUrl = item.ImageUrl || item.Url || "";
      let display = item.DisplayText || item.ShowText;

      // 隨機顏色
      if (Config.QUICKREPLY_RANDOM_COLOR) {
        let arr = new Uint8Array(1);
        const crypto = window.crypto || window.msCrypto;
        crypto.getRandomValues(arr);
        let randomNum = arr[0] * Math.pow(2, -8);
        colorIndex = Math.floor(
          randomNum * Config.QUICKREPLY_BUTTON_COLOR.length
        );
      } else {
        colorIndex = i % Config.QUICKREPLY_BUTTON_COLOR.length;
      }

      switch (item.Option) {
        case "QA":
        case "Option":
          card_code = encodeURIComponent(
            JSON.stringify({
              FCode: item.Code,
              FDisplayText: display,
              FShowText: item.ShowText,
            })
          );
          entity = `
            <div class='QuickReply_div' style="background-color:{backgroundColor}; border:{borderColor} solid; color:{color};"
                 tabindex="0" onkeypress="ChatEvent.onAnswerButtonClick('Option','${card_code}');" onclick="ChatEvent.onAnswerButtonClick('Option','${card_code}');">
              ${item.ShowText}
            </div>`;
          if (Config.QUICKREPLY_OPEN_CHANGE) {
            if (Config.QUICKREPLY_RANDOM_COLOR && Config.QUICKREPLY_BUTTON_COLOR.length > 0) {
              if (Config.QUICKREPLY_BUTTON_STYLE === "background") {
                let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];
                entity = entity
                  .replace("{backgroundColor}", color)
                  .replace("{borderColor}", color)
                  .replace("{color}", "white");
              } else if (Config.QUICKREPLY_BUTTON_STYLE === "border") {
                let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];
                entity = entity
                  .replace("{backgroundColor}", "white")
                  .replace("{borderColor}", color)
                  .replace("{color}", color);
              }
            }
          }
          break;
        case "Url":
          entity = `
            <a class='swiper_li' href='${item.Code}' target='_blank' tabindex="0">
              ${item.ShowText}
            </a>`;
          break;
      }

      QbiQuickReply.appendBtn(
        Config.QUICKREPLY_DIRECTION,
        entity,
        colorIndex,
        imgUrl
      );
    }

    if (Config.QUICKREPLY_DIRECTION == "Horizontal") {
      QbiQuickReply.quickReplyPool.slideTo(0);
    }

    // 檢查是否需要顯示 more-reply 圖示
    QbiQuickReply.checkMoreReplyIcon();
    
    QbiQuickReply.refreshMessageListLayout();
  },

  // Create QuickReply pool
  initQuickReplyPool: function () {
    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        QbiQuickReply.quickReplyPool = $("#QuickReply_Container");
        break;
      case "Horizontal":
        QbiQuickReply.quickReplyPool = new Swiper("#QuickReply_Container", {
          mode: "horizontal",
          loop: false,
          slidesPerView: "auto",
          grabCursor: true,
          spaceBetween: 10,
        });
        break;
      default:
        break;
    }
    
    // 初始化視窗大小變化監聽器
    QbiQuickReply.initResizeListener();
  },

  // Render QuickReply button
  appendBtn: function (direction, entity, colorIndex, imgUrl) {
    let imgDisplay = imgUrl ? "" : "display:none;";
    let color = Config.QUICKREPLY_BUTTON_COLOR[colorIndex];

    let template;
    switch (direction) {
      case "Vertical":
        template =
          Config.QUICKREPLY_BUTTON_STYLE === "background"
            ? QbiQuickReply.templates.verticalBackground
            : QbiQuickReply.templates.verticalBorder;
        break;
      case "Horizontal":
        template =
          Config.QUICKREPLY_BUTTON_STYLE === "background"
            ? QbiQuickReply.templates.horizontalBackground
            : QbiQuickReply.templates.horizontalBorder;
        break;
      default:
        return;
    }

    const html = template
      .replace("{imgUrl}", imgUrl)
      .replace("{imgDisplay}", imgDisplay)
      .replace("{entity}", entity);

    if (direction === "Vertical") {
      $("#QuickReply")[0].insertAdjacentHTML(
        "beforeend",
        DOMPurify.sanitize(html, {
          ADD_ATTR: ["onclick", "reply", "q", "onkeypress"],
        })
      );
    } else {
      QbiQuickReply.quickReplyPool.appendSlide(html);
    }
  },

  // Clear QuickReply pool
  closeQuickReplyPool: function () {
    $("#ToolZone").hide();
    QbiQuickReply.refreshMessageListLayout();

    // 移除 more-reply 圖示
    $("#more-reply-icon").remove();

    // 清理 transform observer
    if (QbiQuickReply.transformObserver) {
      QbiQuickReply.transformObserver.disconnect();
      QbiQuickReply.transformObserver = null;
    }

    switch (Config.QUICKREPLY_DIRECTION) {
      case "Vertical":
        $("#QuickReply").empty();
        break;
      case "Horizontal":
        QbiQuickReply.quickReplyPool.removeAllSlides();
        break;
    }
  },

  refreshMessageListLayout: function () {
    let toolHeight = 0;
    let messagelistBottom = 0;
    let DirKnowledgeHeight = 0;

    let tool = $("#ToolZone");
    let DirKnowledge = $("#DirKnowledge");
    let sendMessage = $("#sendMessage");

    DirKnowledgeHeight = parseInt(DirKnowledge.outerHeight());
    messagelistBottom = parseInt(sendMessage.outerHeight());
    if (tool.is(":visible")) toolHeight = parseInt(tool.outerHeight());

    tool.css("bottom", messagelistBottom + "px");

    const listBottom = toolHeight + messagelistBottom + DirKnowledgeHeight;
    document.getElementById(
      "messageBox"
    ).style.height = `calc(100vh - ${listBottom}px)`;
  },

  // 檢查並控制 more-reply 圖示顯示
  checkMoreReplyIcon: function() {
    if (Config.QUICKREPLY_DIRECTION === "Horizontal") {
      const container = $("#QuickReply");
      const containerWidth = container.width();
      const buttons = container.find('.QuickReply_div');
      
      if (buttons.length <= 1) {
        QbiQuickReply.hideMoreReplyIcon();
        return;
      }
      
      let visibleButtonCount = 0;
      let totalWidth = 0;
      const moreIconWidth = 30; // more-reply 圖示預估寬度
      
      // 計算能完整顯示的按鈕數量
      buttons.each(function() {
        const buttonWidth = $(this).outerWidth(true);
        if (totalWidth + buttonWidth + moreIconWidth <= containerWidth) {
          totalWidth += buttonWidth;
          visibleButtonCount++;
        } else {
          return false; // 跳出 each 循環
        }
      });
      
      // 如果所有按鈕都能完整顯示，隱藏 more-reply 圖示
      if (visibleButtonCount >= buttons.length) {
        QbiQuickReply.hideMoreReplyIcon();
      } else {
        QbiQuickReply.showMoreReplyIcon(visibleButtonCount);
      }
    }
  },

  // 顯示 more-reply 圖示
  showMoreReplyIcon: function(insertAfterIndex) {
    // 如果圖示已存在，只需要調整位置
    if ($("#more-reply-icon").length > 0) {
      const buttons = $("#QuickReply").find('.QuickReply_div');
      if (insertAfterIndex > 0 && insertAfterIndex <= buttons.length) {
        $(buttons[insertAfterIndex - 1]).parent().after($("#more-reply-icon"));
      }
      $("#more-reply-icon").show();
      return;
    }
    
    // 只有在圖示不存在時才建立新的
    const moreIcon = `
      <div id="more-reply-icon" class="QuickreplySlide" style="display: flex; align-items: center; padding: 2px; margin: 3px 1px; flex-shrink: 0;">
        <div style="display: flex; align-items: center; height: 32px; padding: 0 10px; background: rgba(70, 143, 222, 0.1); border-radius: 20px; border: 1px solid #468FDE;">
          <img src="../../image/more-reply.svg" alt="More replies" style="width: 16px; height: 16px; opacity: 0.7;">
        </div>
      </div>
    `;
    
    const buttons = $("#QuickReply").find('.QuickReply_div');
    if (insertAfterIndex > 0 && insertAfterIndex <= buttons.length) {
      // 在指定按鈕後面插入 more-reply 圖示
      $(buttons[insertAfterIndex - 1]).parent().after(moreIcon);
    } else {
      // 如果沒有指定位置，添加到最後
      $("#QuickReply").append(moreIcon);
    }
  },

  // 隱藏 more-reply 圖示
  hideMoreReplyIcon: function() {
    $("#more-reply-icon").hide();
  },

  // 初始化拖動監聽器
  initDragListener: function() {
    let isScrolling = false;
    
    // 根據方向選擇正確的滾動容器
    const scrollContainer = Config.QUICKREPLY_DIRECTION === "Horizontal" 
      ? $("#QuickReply") 
      : $("#QuickReply_Container");
    
    // 監聽滾動事件
    scrollContainer.on('scroll', function() {
      if (!isScrolling) {
        QbiQuickReply.hideMoreReplyIcon();
        isScrolling = true;
      }
    });
    
    // 使用 MutationObserver 監聽 transform 樣式變化
    QbiQuickReply.initTransformObserver();
    
    // 滾動結束後重新檢查
    let scrollTimeout;
    scrollContainer.on('scroll', function() {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        isScrolling = false;
        QbiQuickReply.checkMoreReplyIcon();
      }, 150);
    });
  },

  // 初始化 transform 樣式變化監聽器
  initTransformObserver: function() {
    let lastTransform = '';

    // 監聽目標元素列表
    const observeTargets = [];

    // 添加 QuickReply 主容器
    const quickReplyElement = document.getElementById('QuickReply');
    if (quickReplyElement) {
      observeTargets.push(quickReplyElement);
    }

    // 添加 QuickReply_Container (Swiper 容器)
    const quickReplyContainer = document.getElementById('QuickReply_Container');
    if (quickReplyContainer) {
      observeTargets.push(quickReplyContainer);

      // 添加 Swiper wrapper (通常是 .swiper-wrapper)
      const swiperWrapper = quickReplyContainer.querySelector('.swiper-wrapper');
      if (swiperWrapper) {
        observeTargets.push(swiperWrapper);
      }
    }

    // 創建 MutationObserver
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const target = mutation.target;
          const currentTransform = target.style.transform;

          // 檢查是否有 transform 變化
          if (currentTransform && currentTransform !== lastTransform) {
            QbiQuickReply.hideMoreReplyIcon();
            lastTransform = currentTransform;

            // 延遲檢查是否需要重新顯示圖示
            setTimeout(() => {
              QbiQuickReply.checkMoreReplyIcon();
            }, 200);
          }
        }
      });
    });

    // 對所有目標元素進行監聽
    observeTargets.forEach(function(target) {
      observer.observe(target, {
        attributes: true,
        attributeFilter: ['style'],
        subtree: true // 監聽子元素的變化
      });
    });

    // 儲存 observer 以便後續清理
    QbiQuickReply.transformObserver = observer;
  },

  // 監聽視窗大小變化
  initResizeListener: function() {
    $(window).on('resize', function() {
      setTimeout(() => {
        QbiQuickReply.checkMoreReplyIcon();
      }, 100);
    });
    
    // 初始化拖動監聽器
    QbiQuickReply.initDragListener();
  }
};









